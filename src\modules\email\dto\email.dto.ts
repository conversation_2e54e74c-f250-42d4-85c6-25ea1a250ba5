import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class CreateNotificationDto {
  @ApiProperty({ description: 'EmailTo', example: '<EMAIL>' })
  @IsString({ message: 'EmailTo must be a string' })
  @IsNotEmpty({ message: 'EmailTo is required' })
  to: string;

  @ApiProperty({ description: 'text', example: 'Hello' })
  @IsString({ message: 'text must be a string' })
  @IsOptional()
  text?: string;

  @ApiProperty({ description: 'subject', example: 'Subject' })
  @IsString({ message: 'subject must be a string' })
  @IsNotEmpty({ message: 'subject is required' })
  subject: string;

  @ApiProperty({ description: 'html', example: '<div>hello</div>' })
  @IsOptional()
  html?: string;

  @ApiProperty({ description: 'templateId', example: 'templateId' })
  @IsOptional()
  templateId?: string;

  @ApiProperty({ description: 'variables', example: 'variables' })
  @IsOptional()
  variables?: any;
}
