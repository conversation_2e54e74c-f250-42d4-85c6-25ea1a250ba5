import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Resend } from 'resend';
import { EnvConfigType } from 'src/config/env.config';
import { CreateNotificationDto } from '../dto/email.dto';
import { IEmailProvider } from './email-provider.interface';
import { EmailTemplateService } from './template.service';
import { ErrorHelper } from 'src/common/services/error.helper';
import { ErrorMessages } from '../constants/error.constants';

@Injectable()
export class ResendService implements IEmailProvider {
  private resend: Resend;
  private readonly logger = new Logger(ResendService.name);

  constructor(
    private config: ConfigService<EnvConfigType>,
    private emailTemplateService: EmailTemplateService,
  ) {
    this.resend = new Resend(config.get('email.resendApiKey'));
  }

  async sendEmail(data: CreateNotificationDto): Promise<any> {
    const { to, subject, text, html } = data;

    if (this.config.get('mode') === 'development') {
      return null;
    }

    try {
      const response = await this.resend.emails.send({
        from: this.config.get('email.resendFromEmail'),
        to: [to],
        subject: subject,
        text: text,
        html: html,
      });

      this.logger.log(
        'Email sent via Resend:',
        this.config.get('email.resendFromEmail'),
        to,
        response,
      );
      this.logger.error('Failed to send email via Resend:', data, response);

      if (response.error) {
        ErrorHelper.throwError({
          moduleErrors: ErrorMessages,
          key: 'email/send-failed',
          customMessage:
            response.error?.message ??
            (response.error as any).error ??
            'Failed to send email',
        });
      }

      return response;
    } catch (error) {
      this.logger.error('Failed to send email via Resend:', data, error);
      ErrorHelper.throwError({
        moduleErrors: ErrorMessages,
        key: 'email/send-failed',
        customMessage: error?.message ?? 'Failed to send email',
      });
    }
  }

  async sendEmailWithTemplate(data: CreateNotificationDto): Promise<any> {
    const { variables, templateId } = data;

    const html = this.emailTemplateService.getTemplate(templateId, {
      account_name: 'BookOne',
      support_url: `${this.config.get('app.frontendUrl')}/support`,
      expire_time: '24 giờ',
      ...variables,
    });

    return this.sendEmail({
      ...data,
      html,
    });
  }
}
