import { Injectable } from '@nestjs/common';
import { CreateNotificationDto } from '../../email/dto/email.dto';
import { ConfigService } from '@nestjs/config';
import { EnvConfigType } from 'src/config/env.config';
import { EmailProviderFactory } from 'src/modules/email/service/email-provider.factory';

@Injectable()
export class NotificationService {
  constructor(
    private readonly emailProviderFactory: EmailProviderFactory,
    private readonly config: ConfigService<EnvConfigType>,
  ) {}

  private getEmailProvider() {
    return this.emailProviderFactory.getProvider();
  }

  sendEmailNotification(data: CreateNotificationDto) {
    return this.getEmailProvider().sendEmail(data);
  }

  sendEmailNotificationWithTemplate(data: CreateNotificationDto) {
    return this.getEmailProvider().sendEmailWithTemplate(data);
  }

  sendVerificationEmail(params: {
    email: string;
    verificationToken: string;
    expireTime?: string;
    supportUrl?: string;
  }): Promise<any> {
    const { email, verificationToken, expireTime, supportUrl } = params;

    const data = {
      to: email,
      subject: 'Xác thực tài khoản',
      templateId: 'verify-email',
      variables: {
        name: email,
        expire_time: expireTime,
        support_url: supportUrl,
        account_name: 'BookOne',
        action_url: `${this.config.get('app.frontendUrl')}/auth/verify-email/${verificationToken}`,
      },
    };

    return this.sendEmailNotificationWithTemplate(data);
  }

  sendResetPasswordEmail(params: {
    email: string;
    verificationToken: string;
    expireTime?: string;
    supportUrl?: string;
  }): Promise<any> {
    const { email, verificationToken, expireTime, supportUrl } = params;

    const data = {
      to: email,
      subject: 'Khôi phục mật khẩu',
      templateId: 'reset-password',
      variables: {
        name: email,
        expire_time: expireTime,
        support_url: supportUrl,
        account_name: 'BookOne',
        action_url: `${this.config.get('app.frontendUrl')}/auth/reset-password/${verificationToken}`,
      },
    };

    return this.sendEmailNotificationWithTemplate(data);
  }
}
