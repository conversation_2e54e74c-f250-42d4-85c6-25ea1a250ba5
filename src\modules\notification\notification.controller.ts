import { Body, Controller, HttpException, Post } from '@nestjs/common';
import { NotificationService } from './services/notification.service';
import { CreateNotificationDto } from './dto/notification.dto';

@Controller('notification')
export class NotificationController {
  constructor(private readonly notificationService: NotificationService) {}

  @Post('email')
  async sendNotification(@Body() dto: CreateNotificationDto) {
    try {
      const result = await this.notificationService.sendEmailNotification(dto);
      return {
        message: 'Notification sent successfully',
        data: result,
      };
    } catch (error) {
      throw new HttpException(error?.message, 500);
    }
  }
}
