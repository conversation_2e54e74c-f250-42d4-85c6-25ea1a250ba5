import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { BookingService } from './booking.service';
import { BookingRepository } from './repositories/booking.repository';
import { bookingModelDefinition } from './schemas/booking.schema';
import { CommonModule } from 'src/common/common.module';
import { BookingAgentController } from './controllers/booking-agent.controller';
import { BookingPublicController } from './controllers/booking-public.controller';
import { BookingPageModule } from '../booking-page/booking-page.module';
import { NotificationModule } from '../notification/notification.module';

@Module({
  imports: [
    MongooseModule.forFeature([bookingModelDefinition]),
    CommonModule,
    BookingPageModule,
    NotificationModule,
  ],
  controllers: [BookingAgentController, BookingPublicController],
  providers: [BookingService, BookingRepository],
  exports: [BookingService, BookingRepository],
})
export class BookingModule {}
