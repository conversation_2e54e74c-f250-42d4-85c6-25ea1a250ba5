import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { MailerSend, EmailParams, Recipient, Sender } from 'mailersend';
import { EnvConfigType } from 'src/config/env.config';
import { CreateNotificationDto } from '../dto/email.dto';
import { IEmailProvider } from './email-provider.interface';

@Injectable()
export class MailerSendService implements IEmailProvider {
  private mailerSend: MailerSend;
  private readonly logger = new Logger(MailerSendService.name);

  constructor(private config: ConfigService<EnvConfigType>) {
    this.mailerSend = new MailerSend({
      apiKey: config.get('email.mailersendApiKey'),
    });
  }

  async sendEmail(data: CreateNotificationDto): Promise<any> {
    const { to, subject, text, html, templateId, variables } = data;
    const recipients = [new Recipient(to, to)];

    const sentFrom = new Sender(
      this.config.get('email.mailersendFromEmail'),
      this.config.get('email.adminName'),
    );

    const emailParams = new EmailParams()
      .setFrom(sentFrom)
      .setTo(recipients)
      .setSubject(subject)
      .setText(text)
      .setHtml(html)
      .setTemplateId(templateId)
      .setPersonalization(variables);

    try {
      const response = await this.mailerSend.email.send(emailParams);
      this.logger.log('Email sent via MailerSend:', response.body);
      return response.body;
    } catch (error) {
      this.logger.error('Failed to send email via MailerSend:', data, error);

      throw new Error(
        error?.body?.message ?? error?.message ?? 'Failed to send email',
      );
    }
  }

  async sendEmailWithTemplate(data: CreateNotificationDto): Promise<any> {
    return this.sendEmail({
      ...data,
      templateId: data.templateId,
    });
  }
}
