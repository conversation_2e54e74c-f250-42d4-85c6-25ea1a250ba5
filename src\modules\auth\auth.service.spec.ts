import { Test, TestingModule } from '@nestjs/testing';
import { AuthService } from './auth.service';
import { UserService } from '../user/user.service';
import { RedisService } from '../../common/services/redis.service';
import { ConfigService } from '@nestjs/config';
import { GoogleService } from '../../third-party/google/google.service';
import { HttpException } from '@nestjs/common';

describe('AuthService', () => {
  let service: AuthService;
  let userService: UserService;
  let googleService: GoogleService;

  const mockUserService = {
    findByEmail: jest.fn(),
    createUser: jest.fn(),
    updateUser: jest.fn(),
  };

  const mockRedisService = {
    setEx: jest.fn(),
    get: jest.fn(),
    del: jest.fn(),
  };

  const mockConfigService = {
    get: jest.fn(),
  };

  const mockGoogleService = {
    verifyIdToken: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        { provide: UserService, useValue: mockUserService },
        { provide: RedisService, useValue: mockRedisService },
        { provide: ConfigService, useValue: mockConfigService },
        { provide: GoogleService, useValue: mockGoogleService },
      ],
    }).compile();

    service = module.get<AuthService>(AuthService);
    userService = module.get<UserService>(UserService);
    googleService = module.get<GoogleService>(GoogleService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('loginWithGoogle', () => {
    it('should create a new user if user does not exist', async () => {
      // Arrange
      const googleUser = {
        email: '<EMAIL>',
        name: 'Test User',
        googleId: 'google123',
      };

      mockGoogleService.verifyIdToken.mockResolvedValue(googleUser);
      mockUserService.findByEmail.mockResolvedValue(null);
      mockUserService.createUser.mockResolvedValue({
        _id: 'user123',
        ...googleUser,
        emailVerified: true,
      });

      // Mock token generation
      jest.spyOn(service, 'generateToken').mockResolvedValue({
        accessToken: 'access123',
        refreshToken: 'refresh123',
        expiresIn: 3600,
      });

      // Act
      const result = await service.loginWithGoogle({ idToken: 'token123' });

      // Assert
      expect(mockUserService.findByEmail).toHaveBeenCalledWith(
        googleUser.email,
      );
      expect(mockUserService.createUser).toHaveBeenCalledWith({
        email: googleUser.email,
        name: googleUser.name,
        googleId: googleUser.googleId,
        emailVerified: true,
        password: '',
      });
      expect(result).toEqual({
        accessToken: 'access123',
        refreshToken: 'refresh123',
        expiresIn: 3600,
      });
    });

    it('should link Google account if user exists with verified email', async () => {
      // Arrange
      const googleUser = {
        email: '<EMAIL>',
        name: 'Test User',
        googleId: 'google123',
      };

      const existingUser = {
        _id: 'user123',
        email: '<EMAIL>',
        name: 'Test User',
        emailVerified: true,
      };

      mockGoogleService.verifyIdToken.mockResolvedValue(googleUser);
      mockUserService.findByEmail.mockResolvedValue(existingUser);
      mockUserService.updateUser.mockResolvedValue({
        ...existingUser,
        googleId: googleUser.googleId,
      });

      // Mock token generation
      jest.spyOn(service, 'generateToken').mockResolvedValue({
        accessToken: 'access123',
        refreshToken: 'refresh123',
        expiresIn: 3600,
      });

      // Act
      const result = await service.loginWithGoogle({ idToken: 'token123' });

      // Assert
      expect(mockUserService.findByEmail).toHaveBeenCalledWith(
        googleUser.email,
      );
      expect(mockUserService.updateUser).toHaveBeenCalledWith(
        existingUser._id,
        {
          googleId: googleUser.googleId,
        },
      );
      expect(result).toEqual({
        accessToken: 'access123',
        refreshToken: 'refresh123',
        expiresIn: 3600,
      });
    });

    it('should throw error if user exists with unverified email', async () => {
      // Arrange
      const googleUser = {
        email: '<EMAIL>',
        name: 'Test User',
        googleId: 'google123',
      };

      const existingUser = {
        _id: 'user123',
        email: '<EMAIL>',
        name: 'Test User',
        emailVerified: false, // Unverified email
      };

      mockGoogleService.verifyIdToken.mockResolvedValue(googleUser);
      mockUserService.findByEmail.mockResolvedValue(existingUser);

      // Act & Assert
      await expect(
        service.loginWithGoogle({ idToken: 'token123' }),
      ).rejects.toThrow(HttpException);
      expect(mockUserService.findByEmail).toHaveBeenCalledWith(
        googleUser.email,
      );
      expect(mockUserService.updateUser).not.toHaveBeenCalled();
    });
  });
});
