### Create a new booking
POST http://localhost:3000/public/booking
Content-Type: application/json

{
  "slug": "the-b-hoang-van-thu",
  "bookingPageId": "68261cde094b5f3e38d3ae25",
  "customerName": "<PERSON><PERSON> <PERSON>",
  "customerEmail": "<EMAIL>",
  "customerPhone": "0364982187",
  "bookingSlots": [
    {
      "date": "2025-05-22T14:48:29.780Z",
      "field": "field-1",
      "time": "08:00"
    }
  ],
  "paymentMethod": "COD",
  "quantity": 1
}

### Get bookings by email
GET http://localhost:3000/public/booking?email=<EMAIL>

### Get booking by ID
GET http://localhost:3000/public/booking/BOOKING_ID_HERE

### Agent: Get bookings for a booking page
GET http://localhost:3000/agent/booking?bookingPageId=68261cde094b5f3e38d3ae25
Authorization: Bearer YOUR_TOKEN_HERE

### Agent: Update booking status
PUT http://localhost:3000/agent/booking/BOOKING_ID_HERE/status
Content-Type: application/json
Authorization: Bearer YOUR_TOKEN_HERE

{
  "status": "confirmed"
}
