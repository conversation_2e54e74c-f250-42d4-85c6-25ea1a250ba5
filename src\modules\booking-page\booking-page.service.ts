import { Injectable } from '@nestjs/common';
import { BookingPageRepository } from './repositories/booking-page.repository';
import { CreateBookingPageDto } from './dto/create-booking-page.dto';
import { UpdateBookingPageDto } from './dto/update-booking-page.dto';
import { BookingPage } from './schemas/booking-page.schema';
import { ErrorHelper } from 'src/common/services/error.helper';
import { ErrorMessages } from './constants/error.constants';

@Injectable()
export class BookingPageService {
  constructor(private readonly bookingPageRepository: BookingPageRepository) {}

  async create(
    createBookingPageDto: CreateBookingPageDto,
  ): Promise<BookingPage> {
    // Check if slug already exists
    const existingPage = await this.bookingPageRepository.findBySlug(
      createBookingPageDto.slug,
    );
    if (existingPage) {
      ErrorHelper.throwError({
        moduleErrors: ErrorMessages,
        key: 'booking-page/slug-exists',
      });
    }

    // Create the booking page
    return this.bookingPageRepository.create({
      ...createBookingPageDto,
      createdAt: createBookingPageDto.createdAt
        ? new Date(createBookingPageDto.createdAt)
        : new Date(),
      status: createBookingPageDto.status || 'active',
    });
  }

  async findAll(options?: {
    ownerId?: string;
    includePrivate?: boolean;
  }): Promise<BookingPage[]> {
    const query: any = {};

    // If ownerId is provided, filter by owner
    if (options?.ownerId) {
      query.ownerId = options.ownerId;
    } else if (!options?.includePrivate) {
      // If not filtering by owner and not including private pages, only return public pages
      query.isPublic = true;
    }

    // Use the custom find method from the repository that accepts a query
    return this.bookingPageRepository.find(query);
  }

  async findOne(
    id: string,
    options?: { ownerId?: string },
  ): Promise<BookingPage> {
    const page = await this.bookingPageRepository.findById(id);
    if (!page) {
      ErrorHelper.throwError({
        moduleErrors: ErrorMessages,
        key: 'booking-page/not-found',
      });
    }

    // Nếu không có options.ownerId, đây là truy cập public và chỉ trả về trang public
    if (!options?.ownerId && !page.isPublic) {
      ErrorHelper.throwError({
        moduleErrors: ErrorMessages,
        key: 'booking-page/not-found', // Không tiết lộ rằng trang tồn tại nhưng là private
      });
    }

    // Nếu có options.ownerId, kiểm tra xem người dùng có phải là chủ sở hữu không
    if (
      options?.ownerId &&
      !page.isPublic &&
      page.ownerId.toString() !== options.ownerId
    ) {
      ErrorHelper.throwError({
        moduleErrors: ErrorMessages,
        key: 'booking-page/access-denied',
      });
    }

    return page;
  }

  async findBySlug(
    slug: string,
    options?: { ownerId?: string },
  ): Promise<BookingPage> {
    console.log(`Finding booking page by slug: ${slug}, options:`, options);

    try {
      const page = await this.bookingPageRepository.findBySlug(slug);

      if (!page) {
        console.log(`Booking page with slug ${slug} not found`);
        ErrorHelper.throwError({
          moduleErrors: ErrorMessages,
          key: 'booking-page/not-found',
        });
      }

      console.log(
        `Found booking page: ${page._id}, isPublic: ${page.isPublic}, status: ${page.status}`,
      );

      // Nếu không có options.ownerId, đây là truy cập public và chỉ trả về trang public
      if (!options?.ownerId && !page.isPublic) {
        console.log('Access denied: Page is private and no owner ID provided');
        ErrorHelper.throwError({
          moduleErrors: ErrorMessages,
          key: 'booking-page/not-found', // Không tiết lộ rằng trang tồn tại nhưng là private
        });
      }

      // Nếu có options.ownerId, kiểm tra xem người dùng có phải là chủ sở hữu không
      if (
        options?.ownerId &&
        !page.isPublic &&
        page.ownerId.toString() !== options.ownerId
      ) {
        console.log(
          'Access denied: User is not the owner of this private page',
        );
        ErrorHelper.throwError({
          moduleErrors: ErrorMessages,
          key: 'booking-page/access-denied',
        });
      }

      return page;
    } catch (error) {
      console.error(`Error finding booking page by slug ${slug}:`, error);

      // Re-throw the error if it's already a known error
      if (error.response && error.response.code) {
        throw error;
      }

      // Otherwise, throw a generic error
      ErrorHelper.throwError({
        moduleErrors: ErrorMessages,
        key: 'booking-page/not-found',
      });
    }
  }

  async update(
    id: string,
    updateBookingPageDto: UpdateBookingPageDto,
    ownerId: string,
  ): Promise<BookingPage> {
    // Check if page exists and user has access
    const page = await this.findOne(id);

    // Verify ownership - only the owner can update the page
    if (page.ownerId.toString() !== ownerId) {
      ErrorHelper.throwError({
        moduleErrors: ErrorMessages,
        key: 'booking-page/access-denied',
      });
    }

    // Check if slug is being updated and if it already exists
    if (updateBookingPageDto.slug) {
      const existingPage = await this.bookingPageRepository.findBySlug(
        updateBookingPageDto.slug,
      );
      if (existingPage && existingPage._id.toString() !== id) {
        ErrorHelper.throwError({
          moduleErrors: ErrorMessages,
          key: 'booking-page/slug-exists',
        });
      }
    }

    // Update the booking page
    const updatedPage = await this.bookingPageRepository.update(
      id,
      updateBookingPageDto,
    );
    if (!updatedPage) {
      ErrorHelper.throwError({
        moduleErrors: ErrorMessages,
        key: 'booking-page/update-failed',
      });
    }
    return updatedPage;
  }

  async remove(id: string, ownerId: string): Promise<void> {
    // Check if page exists
    const page = await this.findOne(id);

    // Verify ownership - only the owner can delete the page
    if (page.ownerId.toString() !== ownerId) {
      ErrorHelper.throwError({
        moduleErrors: ErrorMessages,
        key: 'booking-page/access-denied',
      });
    }

    // Delete the booking page
    await this.bookingPageRepository.findByIdAndDelete(id);
  }
}
