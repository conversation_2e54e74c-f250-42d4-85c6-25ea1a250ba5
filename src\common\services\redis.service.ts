import {
  Injectable,
  OnM<PERSON>ule<PERSON><PERSON>roy,
  OnModuleInit,
  Logger,
} from '@nestjs/common';
import Redis from 'ioredis';

@Injectable()
export class RedisService implements OnModuleInit, OnModuleDestroy {
  private redisClient: Redis;
  private readonly logger = new Logger(RedisService.name);

  async onModuleInit() {
    if (this.redisClient && this.redisClient.status === 'connect') {
      this.logger.log('Redis connection already established');
      return;
    }

    if (this.redisClient) {
      this.logger.warn('Existing Redis connection found. Closing it.');
      await this.redisClient.quit();
    }

    this.redisClient = new Redis(process.env.REDIS_URI, {
      keyPrefix: process.env.REDIS_PREFIX,
    });

    this.redisClient.on('connect', () => {
      this.logger.log('Connected to Redis');
    });

    this.redisClient.on('error', (err) => {
      this.logger.error('Redis connection error', err);

      const maxRetries = 5;
      let retryCount = 0;

      if (retryCount < maxRetries) {
        retryCount++;
        setTimeout(() => {
          this.redisClient.connect();
        }, 2000);
      } else {
        this.logger.error('Max retry attempts reached');
      }
    });

    this.redisClient.on('error', (err) => {
      this.logger.error('Redis connection error', err);
    });
  }

  onModuleDestroy() {
    this.redisClient.quit();
  }

  async setEx(key: string, value: any, ttl: number): Promise<void> {
    try {
      await this.redisClient.setex(key, ttl, JSON.stringify(value));
    } catch (error) {
      this.logger.error(`Failed to set key ${key} in Redis`, error);
      throw error;
    }
  }

  async get(key: string): Promise<any> {
    try {
      const result = await this.redisClient.get(key);

      return result ? JSON.parse(result) : null;
    } catch (error) {
      this.logger.error(`Failed to get key ${key} in Redis`, error);
      throw error;
    }
  }

  async del(key: string): Promise<void> {
    try {
      await this.redisClient.del(key);
    } catch (error) {
      this.logger.error(`Failed to delete key ${key} in Redis`, error);
      throw error;
    }
  }
}
