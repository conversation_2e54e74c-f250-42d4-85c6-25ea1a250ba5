import { Injectable } from '@nestjs/common';
import * as bcrypt from 'bcrypt';
import { UserRepository } from './repositories/user.repository';
import { ICreateUserPayload } from './dto/signup.dto';
import * as crypto from 'crypto';
import { RedisService } from 'src/common/services/redis.service';
import { User } from './schemas/user.schema';
import { NotificationService } from '../notification/services/notification.service';
import { ErrorHelper } from 'src/common/services/error.helper';
import { ErrorMessages } from '../auth/constants/error.constants';

@Injectable()
export class UserService {
  constructor(
    private readonly userRepository: UserRepository,
    private readonly notificationService: NotificationService,
    private readonly redisService: RedisService,
  ) {}

  private generateVerificationToken() {
    return `${crypto.randomBytes(32).toString('hex')}-${Date.now()}`;
  }

  private getVerificationKey(token: string) {
    return `email_verification:${token}`;
  }

  private getRateLimitKey(email: string, type: 'minute' | 'day') {
    return `email_verification_rate_limit:${email}:${type}`;
  }

  async createUser(dataRaw: ICreateUserPayload) {
    const hashedPassword = await bcrypt.hash(dataRaw?.password, 10);

    const data = {
      ...dataRaw,
      password: hashedPassword,
      emailVerified: false,
    };

    const user = await this.userRepository.createUser(data);

    if (!user) {
      ErrorHelper.throwError({
        moduleErrors: ErrorMessages,
        key: 'account/create-user-failed',
      });
    }

    return user;
  }

  handleSendVerificationEmail = async (user: User) => {
    // Tạo token xác thực email
    const verificationToken = this.generateVerificationToken();

    // Lưu token vào Redis với TTL 24 giờ (86400 giây)
    await this.redisService.setEx(
      this.getVerificationKey(verificationToken),
      { userId: user._id },
      86400,
    );

    await this.notificationService.sendVerificationEmail({
      email: user.email,
      verificationToken,
      expireTime: '24 giờ',
    });
  };

  async verifyEmail(token: string) {
    // Kiểm tra token xác thực email
    const verificationData = await this.redisService.get(
      this.getVerificationKey(token),
    );

    if (!verificationData) {
      ErrorHelper.throwError({
        moduleErrors: ErrorMessages,
        key: 'account/invalid-verification-token',
      });
    }

    const user = await this.userRepository.findById(verificationData.userId);

    if (!user) {
      ErrorHelper.throwError({
        moduleErrors: ErrorMessages,
        key: 'account/email-not-found',
      });
    }

    if (user.emailVerified) {
      return { message: 'Email verified successfully' };
    }

    user.emailVerified = true;
    await this.userRepository.findByIdAndUpdate(user._id, user);

    // Xóa token khỏi Redis sau khi verify thành công
    await this.redisService.del(this.getVerificationKey(token));

    return { message: 'Email verified successfully' };
  }

  /**
   * Check rate limits for email verification requests
   * @param email User email
   * @throws UnauthorizedException if rate limit is exceeded
   */
  private async checkRateLimits(email: string) {
    // Check minute rate limit (1 request per minute)
    const minuteKey = this.getRateLimitKey(email, 'minute');
    const minuteCount = await this.redisService.get(minuteKey);

    if (minuteCount) {
      ErrorHelper.throwError({
        moduleErrors: ErrorMessages,
        key: 'account/rate-limit-minute',
      });
    }

    // Set minute rate limit (expires in 60 seconds)
    await this.redisService.setEx(minuteKey, { count: 1 }, 60);

    // Check daily rate limit (5 requests per day)
    const dayKey = this.getRateLimitKey(email, 'day');
    const dayCount = (await this.redisService.get(dayKey)) || { count: 0 };

    if (dayCount.count >= 5) {
      ErrorHelper.throwError({
        moduleErrors: ErrorMessages,
        key: 'account/rate-limit-day',
      });
    }

    // Increment daily count
    await this.redisService.setEx(
      dayKey,
      { count: dayCount.count + 1 },
      86400, // 24 hours
    );
  }

  /**
   * Resend verification email by user ID (for authenticated users)
   * @param userId User ID
   * @returns Success message
   */
  async resendVerificationEmail(userId: string) {
    try {
      const user = await this.userRepository.findById(userId);

      if (!user) {
        ErrorHelper.throwError({
          moduleErrors: ErrorMessages,
          key: 'account/email-not-found',
        });
      }

      if (user.emailVerified) {
        ErrorHelper.throwError({
          moduleErrors: ErrorMessages,
          key: 'account/already-verified',
        });
      }

      // Check rate limits
      await this.checkRateLimits(user.email);

      // Send verification email
      await this.handleSendVerificationEmail(user);
      return { message: 'Verification email sent successfully' };
    } catch (error) {
      ErrorHelper.throwError({
        moduleErrors: ErrorMessages,
        key: 'account/internal-server-error',
        customMessage: error?.message,
      });
    }
  }

  /**
   * Resend verification email by email address (for non-authenticated users)
   * @param email User email
   * @returns Success message
   */
  async resendVerificationEmailByEmail(email: string) {
    const user = await this.userRepository.findOne({ email });

    if (!user) {
      ErrorHelper.throwError({
        moduleErrors: ErrorMessages,
        key: 'account/email-not-found',
      });
    }

    if (user.emailVerified) {
      ErrorHelper.throwError({
        moduleErrors: ErrorMessages,
        key: 'account/already-verified',
      });
    }

    // Check rate limits
    await this.checkRateLimits(email);

    // Send verification email
    await this.handleSendVerificationEmail(user);
    return { message: 'Verification email sent successfully' };
  }

  // Tìm người dùng đầy đủ theo email
  async findFullUserByEmail(email: string) {
    return this.userRepository.findUserHasPasswordByEmail(email);
  }

  // Tìm người dùng theo email
  async findByEmail(email: string) {
    return this.userRepository.findOne({ email });
  }

  async findUserById(userId: string) {
    return this.userRepository.findOne({ _id: userId });
  }

  updateUser(id: string, updateDto: any) {
    return this.userRepository.findByIdAndUpdate(id, updateDto);
  }
}
