import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { ErrorMessages } from './error.constants';
import { RedisService } from 'src/common/services/redis.service';
import { getAccessToken } from 'src/constants/auth';
import { ErrorHelper } from '../services/error.helper';

@Injectable()
export class AuthGuard implements CanActivate {
  constructor(private readonly redisService: RedisService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const req = context.switchToHttp().getRequest();
    const token = this.extractTokenFromHeader(req);

    if (!token) {
      ErrorHelper.throwError({
        moduleErrors: ErrorMessages,
        key: 'auth/missing-token',
      });
    }

    try {
      const user = await this.redisService.get(getAccessToken(token));
      // Gắn thông tin user vào request để sử dụng trong các controller hoặc middleware.

      if (!user) {
        ErrorHelper.throwError({
          moduleErrors: ErrorMessages,
          key: 'auth/invalid-token',
        });
      }

      req.user = user;
      return true;
    } catch (error) {
      ErrorHelper.throwError({
        moduleErrors: ErrorMessages,
        key: 'auth/invalid-token',
        customMessage: error?.message,
      });
    }
  }

  private extractTokenFromHeader(req: any): string | null {
    const authHeader = req.headers['authorization'];
    if (!authHeader) {
      return null;
    }

    const parts = authHeader.split(' ');
    if (parts.length !== 2 || parts[0] !== 'Bearer') {
      return null;
    }

    return parts[1];
  }
}
