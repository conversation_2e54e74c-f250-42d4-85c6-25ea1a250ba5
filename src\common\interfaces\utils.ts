export type DeepKey<T> = T extends object
  ? {
      [K in keyof T]: K extends string ? `${K}.${DeepKey<T[K]>}` : never;
    }[keyof T]
  : never;

export type Paths<T> = T extends object
  ? {
      [K in keyof T]: `${Exclude<K, symbol>}${'' | `.${Paths<T[K]>}`}`;
    }[keyof T]
  : never;

export type Leaves<T> = T extends object
  ? {
      [K in keyof T]: `${Exclude<K, symbol>}${Leaves<T[K]> extends never ? '' : `.${Leaves<T[K]>}`}`;
    }[keyof T]
  : never;
