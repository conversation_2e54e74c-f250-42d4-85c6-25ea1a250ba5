import { Modu<PERSON> } from '@nestjs/common';
import { NotificationController } from './notification.controller';
import { NotificationService } from './services/notification.service';
import { EmailModule } from '../email/email.module';
import { MongooseModule } from '@nestjs/mongoose';
import { notificationModelDefinition } from './schemas/notification.schema';

@Module({
  imports: [
    MongooseModule.forFeature([notificationModelDefinition]),
    EmailModule,
  ],
  controllers: [NotificationController],
  providers: [NotificationService],
  exports: [NotificationService],
})
export class NotificationModule {}
