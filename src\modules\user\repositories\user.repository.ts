import { BadRequestException, Injectable } from '@nestjs/common';
import { User } from '../schemas/user.schema';
import { BaseRepository } from 'src/common/repositories/base.repository';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

@Injectable()
export class UserRepository extends BaseRepository<User> {
  constructor(@InjectModel(User.name) private readonly userModel: Model<User>) {
    super(userModel); // Truyền model của User cho BaseRepository
  }

  async createUser(data): Promise<User> {
    try {
      return this.create(data);
    } catch (error) {
      throw new BadRequestException({ message: error?.message });
    }
  }

  async findUserHasPasswordByEmail(email: string): Promise<User | null> {
    return this.userModel.findOne({ email }).select('+password').exec();
  }
}
