import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';

export class CreateNotificationDto {
  @ApiProperty({ description: 'EmailTo', example: '<EMAIL>' })
  @IsString({ message: 'EmailTo must be a string' })
  @IsNotEmpty({ message: 'EmailTo is required' })
  to: string;

  @ApiProperty({ description: 'text', example: 'Hello' })
  @IsString({ message: 'text must be a string' })
  @IsNotEmpty({ message: 'text is required' })
  text: string;

  @ApiProperty({ description: 'subject', example: 'Subject' })
  @IsString({ message: 'subject must be a string' })
  @IsNotEmpty({ message: 'subject is required' })
  subject: string;

  @ApiProperty({ description: 'html', example: '<div>hello</div>' })
  html?: string;
}
