import { Schema } from '@nestjs/mongoose';
import { Document } from 'mongoose';

// Helper function để chuyển _id thành id và xóa version
export function commonTransform(doc: Document, ret: any) {
  ret.id = ret._id.toHexString(); // Chuyển _id thành id
  delete ret._id; // Ẩn trường _id
  delete ret.__v; // Ẩn trường __v (version key)
}

@Schema({
  versionKey: false,
  toJSON: { virtuals: true, transform: commonTransform },
  toObject: { virtuals: true, transform: commonTransform },
})
export abstract class BaseSchema extends Document {
  _id: string;
}
