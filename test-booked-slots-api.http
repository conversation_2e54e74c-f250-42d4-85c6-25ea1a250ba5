### Get booked slots for a specific booking page and date
GET http://localhost:3000/public/booking/slots/booked?bookingPageId=68261cde094b5f3e38d3ae25&date=2025-05-22

### Get booked slots with invalid date format
GET http://localhost:3000/public/booking/slots/booked?bookingPageId=68261cde094b5f3e38d3ae25&date=05/22/2025

### Get booked slots with invalid bookingPageId format
GET http://localhost:3000/public/booking/slots/booked?bookingPageId=invalid-id&date=2025-05-22

### Get booked slots without bookingPageId
GET http://localhost:3000/public/booking/slots/booked?date=2025-05-22

### Get booked slots without date
GET http://localhost:3000/public/booking/slots/booked?bookingPageId=68261cde094b5f3e38d3ae25

### Example response format
# {
#   "data": {
#     "bookingPageId": "68261cde094b5f3e38d3ae25",
#     "date": "2025-05-22",
#     "bookedSlots": [
#       {
#         "field": "field-1",
#         "time": "08:00",
#         "date": "2025-05-22T00:00:00.000Z",
#         "bookingId": "60a1b2c3d4e5f6g7h8i9j0k1",
#         "status": "pending"
#       },
#       {
#         "field": "field-2",
#         "time": "09:00",
#         "date": "2025-05-22T00:00:00.000Z",
#         "bookingId": "60a1b2c3d4e5f6g7h8i9j0k2",
#         "status": "confirmed"
#       }
#     ]
#   }
# }
