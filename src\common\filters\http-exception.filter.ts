import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { Request, Response } from 'express';

@Catch()
export class HttpExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(HttpExceptionFilter.name);

  catch(exception: HttpException, host: ArgumentsHost) {
    try {
      const ctx = host.switchToHttp();
      const response = ctx.getResponse<Response>();
      const request = ctx.getRequest<Request>();

      const status =
        exception instanceof HttpException
          ? exception.getStatus()
          : HttpStatus.INTERNAL_SERVER_ERROR;

      let message: any =
        exception instanceof HttpException
          ? exception.getResponse()
          : {
              message: (exception as Error).message || 'Internal server error',
            };

      // Xử lý trường hợp `message` là object
      if (typeof message === 'object' && message !== null) {
        if (message['message'] && Array.isArray(message['message'])) {
          message = message['message'][0]; // Ghép các lỗi lại thành chuỗi
        } else {
          message = message['message'] || JSON.stringify(message); // Lấy message hoặc stringify
        }
      }

      // Lấy mã lỗi chuỗi trực tiếp từ exception
      const errorCode =
        exception instanceof HttpException &&
        typeof exception.getResponse() === 'object'
          ? exception.getResponse()['code'] ||
            exception.getResponse()['statusCode'] ||
            'unknown/error'
          : 'unknown/error';

      console.log('exception.getResponse()', exception.getResponse());

      const logMessage = `HTTP ${status} Error: ${message} - [${request.method}] ${request.url}`;

      // Log error chi tiết
      this.logger.error(logMessage, exception.stack);

      response.status(200).json({
        status: {
          success: false,
          message,
          code: errorCode, // Trả về mã lỗi chuỗi
        },
      });
    } catch (error: any) {
      this.logger.error(
        exception,
        // JSON.stringify(host, null, 2),
        JSON.stringify(exception.stack),
      );
      const ctx = host.switchToHttp();
      const response = ctx.getResponse<Response>();

      response.status(200).json({
        status: {
          success: false,
          message: error?.message ?? 'UNKNOWN',
          code: 'unknown/error', // Trả về mã lỗi chuỗi
        },
      });
    }
  }
}
