import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class AddAllowedEmailDto {
  @ApiProperty({ description: 'Email to allow', example: '<EMAIL>' })
  @IsEmail({}, { message: 'Email must be a valid email address' })
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    description: 'Description for this allowed email',
    example: 'Company employee',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;
}

export class RemoveAllowedEmailDto {
  @ApiProperty({
    description: 'Email to remove from allowed list',
    example: '<EMAIL>',
  })
  @IsEmail({}, { message: 'Email must be a valid email address' })
  @IsNotEmpty()
  email: string;
}
