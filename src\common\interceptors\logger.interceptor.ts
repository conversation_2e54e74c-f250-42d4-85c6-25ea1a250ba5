import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  <PERSON>H<PERSON>ler,
  Logger,
} from '@nestjs/common';
import { Observable, tap } from 'rxjs';

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  private readonly logger = new Logger(LoggingInterceptor.name);

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const req = context.switchToHttp().getRequest();
    const { method, url, ip } = req;

    const now = Date.now();
    return next.handle().pipe(
      tap(() => {
        const res = context.switchToHttp().getResponse();
        const statusCode = res.statusCode;
        this.logger.log(
          `${method} ${url} ${statusCode} - ${ip} - ${Date.now() - now}ms`,
        );
      }),
    );
  }
}
