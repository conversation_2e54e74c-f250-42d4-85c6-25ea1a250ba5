import { <PERSON>, Get, Req, UseGuards, Param, Put } from '@nestjs/common';
import { UserService } from './user.service';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { AuthGuard } from 'src/common/guards/auth.guard';

@ApiTags('user')
@Controller('user')
@UseGuards(AuthGuard)
export class UserController {
  constructor(private readonly userService: UserService) {}

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get Profile' })
  @ApiResponse({ status: 200, description: 'Signin successfully' })
  @Get('profile')
  async getProfile(@Req() req: any) {
    const data = await this.userService.findUserById(req?.user?._id);

    return { data };
  }

  @Put('verify-email/:token')
  async verifyEmail(@Param('token') token: string) {
    const data = this.userService.verifyEmail(token);

    return { data };
  }

  // @ApiOperation({ summary: 'Resend verification email' })
  // @ApiResponse({
  //   status: 200,
  //   description: 'Verification email sent successfully',
  // })
  // @ApiResponse({ status: 404, description: 'User not found' })
  // @ApiResponse({ status: 429, description: 'Rate limit exceeded' })
  // @Post('resend-verification')
  // @UsePipes(new ValidationPipe())
  // async resendVerification(@Request() req: any) {
  //   const data = await this.userService.resendVerificationEmail(req.user._id);
  //   return { data };
  // }
}
