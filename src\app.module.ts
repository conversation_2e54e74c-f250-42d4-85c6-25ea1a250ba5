import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ConfigService } from '@nestjs/config';

import { AppController } from './app.controller';
import { ConfigurationModule } from './config/config.module';
import { EnvConfigType } from './config/env.config';

import { CommonModule } from './common/common.module';

import { UserModule } from './modules/user/user.module';
import { AuthModule } from './modules/auth/auth.module';
import { BookingPageModule } from './modules/booking-page/booking-page.module';
import { BookingModule } from './modules/booking/booking.module';

@Module({
  imports: [
    ConfigurationModule,
    MongooseModule.forRootAsync({
      imports: [ConfigurationModule],
      useFactory: (configService: ConfigService<EnvConfigType>) => ({
        uri: configService.get('database.uri'),
      }),
      inject: [ConfigService],
    }),
    AuthModule,
    UserModule,
    CommonModule,
    BookingPageModule,
    BookingModule,
  ],
  controllers: [AppController],
  providers: [],
})
export class AppModule {}
