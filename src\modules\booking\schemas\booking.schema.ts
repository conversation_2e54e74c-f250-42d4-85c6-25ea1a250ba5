import { ModelDefinition, Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { BaseSchema } from 'src/common/schemas/base.schema';
import * as mongoose from 'mongoose';

// Interface for booking slots
export interface BookingSlot {
  date: Date;
  field: string;
  fieldName: string;
  time: string;
}

@Schema({
  collection: 'bookings',
  versionKey: false,
})
export class Booking extends BaseSchema {
  @Prop({ required: true })
  slug: string;

  @Prop({ required: true })
  customerName: string;

  @Prop({ required: true })
  customerEmail: string;

  @Prop({ required: true })
  customerPhone: string;

  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: 'BookingPage',
    required: true,
  })
  bookingPageId: string;

  @Prop({ required: true, type: String })
  bookingDate: string;

  @Prop({
    required: true,
    type: [
      {
        date: String,
        field: String,
        fieldName: String,
        time: String,
      },
    ],
    default: [],
  })
  bookingSlots: BookingSlot[];

  @Prop({
    required: true,
    enum: ['COD', 'BANK_TRANSFER', 'CREDIT_CARD', 'MOMO'],
  })
  paymentMethod: string;

  @Prop({ required: false })
  quantity: number;

  @Prop({
    required: true,
    default: 'pending',
    enum: ['pending', 'confirmed', 'cancelled', 'completed'],
  })
  status: string;

  @Prop({ required: true, default: Date.now })
  createdAt: Date;

  @Prop({ required: false })
  updatedAt: Date;
}

export type BookingType = Omit<Booking, keyof BaseSchema | ''>;

export const BookingSchema = SchemaFactory.createForClass(Booking);

export const bookingModelDefinition: ModelDefinition = {
  name: Booking.name,
  schema: BookingSchema,
};
