import { Injectable, Logger } from '@nestjs/common';
import { readFileSync } from 'fs';
import { join } from 'path';

@Injectable()
export class EmailTemplateService {
  private readonly logger = new Logger(EmailTemplateService.name);

  private cache: Map<string, string> = new Map();

  getTemplate(templateName: string, variables: Record<string, string>): string {
    let template = this.cache.get(templateName);

    if (!template) {
      // Trong môi trường production, templates sẽ nằm trong thư mục dist
      const templatePath = join(
        process.cwd(),
        'dist',
        'modules',
        'email',
        'templates',
        `${templateName}.template.html`,
      );
      try {
        template = readFileSync(templatePath, 'utf8');
        this.cache.set(templateName, template);
      } catch (error) {
        this.logger.error(
          `Failed to load template ${templateName} from ${templatePath}: ${error.message}`,
        );
        // Fallback to src directory for development
        const srcTemplatePath = join(
          process.cwd(),
          'src',
          'modules',
          'email',
          'templates',
          `${templateName}.template.html`,
        );
        template = readFileSync(srcTemplatePath, 'utf8');
        this.cache.set(templateName, template);
      }
    }

    return this.replaceVariables(template, variables);
  }

  private replaceVariables(
    template: string,
    variables: Record<string, string>,
  ): string {
    return Object.entries(variables).reduce(
      (result, [key, value]) =>
        result.replace(new RegExp(`{{${key}}}`, 'g'), value),
      template,
    );
  }
}
