import { ModelDefinition, Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { BaseSchema } from 'src/common/schemas/base.schema';
import {
  BannerBlock,
  DescriptionBlock,
  AvailabilityCalendarBlock,
  BookingFormBlock,
} from '../interfaces/blocks.interface';
import { PageTheme } from '../interfaces/theme.interface';
import * as mongoose from 'mongoose';

@Schema({
  collection: 'booking_pages',
  versionKey: false,
})
export class BookingPage extends BaseSchema {
  @Prop({ required: true })
  name: string;

  @Prop({ required: false, default: '' })
  description: string;

  @Prop({ required: true, unique: true })
  slug: string;

  @Prop({ required: true })
  templateCode: string;

  @Prop({ type: Array, required: true, default: [] })
  blocks: (
    | BannerBlock
    | DescriptionBlock
    | AvailabilityCalendarBlock
    | BookingFormBlock
  )[];

  @Prop({ type: Object, required: true })
  theme: PageTheme;

  @Prop({ required: true, default: Date.now })
  createdAt: Date;

  @Prop({
    required: true,
    default: 'active',
    enum: ['active', 'inactive', 'draft'],
  })
  status: string;

  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true })
  ownerId: string;

  @Prop({ type: Boolean, default: true })
  isPublic: boolean;
}

export type BookingPageType = Omit<BookingPage, keyof BaseSchema | ''>;

export const BookingPageSchema = SchemaFactory.createForClass(BookingPage);

export const bookingPageModelDefinition: ModelDefinition = {
  name: BookingPage.name,
  schema: BookingPageSchema,
};
