version: '3.8'

services:
  mongodb:
    image: mongo:6
    container_name: mongodb
    ports:
      - '27017:27017'
    volumes:
      - mongodb_data:/data/db
    environment:
      MONGO_INITDB_ROOT_USERNAME: root
      MONGO_INITDB_ROOT_PASSWORD: rootpassword
      MONGO_INITDB_DATABASE: user-service

  redis:
    image: redis:7
    container_name: redis
    ports:
      - '6379:6379'
    command: ['redis-server', '--save', '', '--appendonly', 'no']

volumes:
  mongodb_data:
