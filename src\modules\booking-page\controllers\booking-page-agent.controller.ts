import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Put,
  UseGuards,
  Query,
  ValidationPipe,
} from '@nestjs/common';
import { BookingPageService } from '../booking-page.service';
import { CreateBookingPageDto } from '../dto/create-booking-page.dto';
import { UpdateBookingPageDto } from '../dto/update-booking-page.dto';
import {
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { AuthGuard } from 'src/common/guards/auth.guard';
import { GetUser } from 'src/common/decorators/get-user.decorator';

@ApiTags('agent/booking-page')
@Controller('agent/booking-page')
@UseGuards(AuthGuard) // Tất cả các route đều yêu cầu xác thực
@ApiBearerAuth()
export class BookingPageAgentController {
  constructor(private readonly bookingPageService: BookingPageService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new booking page' })
  @ApiResponse({
    status: 201,
    description: 'Booking page created successfully',
  })
  async create(
    @Body(new ValidationPipe()) createBookingPageDto: CreateBookingPageDto,
    @GetUser('_id') userId: string,
  ) {
    // Set the owner ID to the current user's ID
    createBookingPageDto.ownerId = userId;
    const data = await this.bookingPageService.create(createBookingPageDto);
    return { data };
  }

  @Get()
  @ApiOperation({ summary: 'Get all booking pages owned by the agent' })
  @ApiResponse({ status: 200, description: 'Return all booking pages' })
  @ApiQuery({
    name: 'includePrivate',
    required: false,
    type: Boolean,
    description: 'Include private pages',
  })
  async findAll(
    @GetUser('_id') userId: string,
    @Query('includePrivate') includePrivate: boolean = true,
  ) {
    // Agent can see all their own pages, including private ones
    const data = await this.bookingPageService.findAll({
      ownerId: userId,
      includePrivate: includePrivate, // Agents always see their own private pages
    });
    return { data };
  }

  @Get('slug/:slug')
  @ApiOperation({ summary: 'Get a booking page by slug' })
  @ApiResponse({ status: 200, description: 'Return the booking page' })
  @ApiResponse({ status: 404, description: 'Booking page not found' })
  async findBySlug(
    @Param('slug') slug: string,
    @GetUser('_id') userId: string,
  ) {
    // Agent can see their own pages
    const data = await this.bookingPageService.findBySlug(slug, {
      ownerId: userId,
    });
    return { data };
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a booking page by ID' })
  @ApiResponse({ status: 200, description: 'Return the booking page' })
  @ApiResponse({ status: 404, description: 'Booking page not found' })
  async findOne(@Param('id') id: string, @GetUser('_id') userId: string) {
    // Agent can see their own pages
    const data = await this.bookingPageService.findOne(id, { ownerId: userId });
    return { data };
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update a booking page' })
  @ApiResponse({
    status: 200,
    description: 'Booking page updated successfully',
  })
  @ApiResponse({ status: 404, description: 'Booking page not found' })
  async update(
    @Param('id') id: string,
    @Body(new ValidationPipe()) updateBookingPageDto: UpdateBookingPageDto,
    @GetUser('_id') userId: string,
  ) {
    const data = await this.bookingPageService.update(
      id,
      updateBookingPageDto,
      userId,
    );
    return { data };
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a booking page' })
  @ApiResponse({
    status: 200,
    description: 'Booking page deleted successfully',
  })
  @ApiResponse({ status: 404, description: 'Booking page not found' })
  async remove(@Param('id') id: string, @GetUser('_id') userId: string) {
    await this.bookingPageService.remove(id, userId);
    return { message: 'Booking page deleted successfully' };
  }
}
