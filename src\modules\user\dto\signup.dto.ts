import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsString,
  IsEmail,
  Min<PERSON><PERSON>th,
  IsOptional,
} from 'class-validator';

export class SignUpDto {
  @ApiProperty({ description: 'Email register', example: '<EMAIL>' })
  @IsEmail({}, { message: 'Email must be a valid email address' })
  @IsNotEmpty()
  email: string;

  @ApiProperty({ description: 'Password register', example: '123123' })
  @IsString({ message: 'Password must be a string' })
  @MinLength(6, { message: 'Password must be at least 6 characters long' })
  password: string;

  @ApiProperty({ description: 'Name', example: '123123' })
  @IsString({ message: 'Name must be a string' })
  @IsNotEmpty({ message: 'Name is required' })
  name: string;

  @IsOptional()
  googleId: string;
}

export interface ICreateUserPayload extends SignUpDto {
  emailVerified?: boolean;
}
