import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { BaseRepository } from 'src/common/repositories/base.repository';
import { Booking } from '../schemas/booking.schema';
import { Model } from 'mongoose';

@Injectable()
export class BookingRepository extends BaseRepository<Booking> {
  constructor(@InjectModel(Booking.name) private bookingModel: Model<Booking>) {
    super(bookingModel);
  }

  async findByBookingPageId(bookingPageId: string): Promise<Booking[]> {
    return this.bookingModel.find({ bookingPageId }).exec();
  }

  async findByCustomerEmail(email: string): Promise<Booking[]> {
    return this.bookingModel.find({ customerEmail: email }).exec();
  }

  /**
   * Override the create method from BaseRepository to add logging
   * @param createDto The booking data
   * @returns The created booking
   */
  async create(createDto: any): Promise<Booking> {
    console.log('Repository: Creating new booking');
    try {
      const createdDoc = new this.bookingModel(createDto);
      const savedDoc = await createdDoc.save();
      console.log(`Repository: Booking created with ID: ${savedDoc._id}`);
      return savedDoc;
    } catch (error) {
      console.error('Repository: Error creating booking:', error);
      throw error;
    }
  }

  /**
   * Check if a booking with the same customer information and booking page already exists
   * @param bookingPageId The ID of the booking page
   * @param customerEmail The email of the customer
   * @param customerPhone The phone number of the customer
   * @returns True if a duplicate booking exists, false otherwise
   */
  async checkDuplicateBooking(
    bookingPageId: string,
    customerEmail: string,
    customerPhone: string,
  ): Promise<boolean> {
    console.log(
      `Checking for duplicate booking: bookingPageId=${bookingPageId}, customerEmail=${customerEmail}, customerPhone=${customerPhone}`,
    );

    try {
      // Calculate the date 24 hours ago
      const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
      console.log(
        `Checking for bookings created after: ${oneDayAgo.toISOString()}`,
      );

      // Use lean() for better performance since we only need to check existence
      const existingBooking = await this.bookingModel
        .findOne({
          bookingPageId,
          customerEmail,
          customerPhone,
          status: { $in: ['pending', 'confirmed'] }, // Only check active bookings
          createdAt: { $gte: oneDayAgo }, // Only check bookings created in the last 24 hours
        })
        .lean()
        .exec();

      if (existingBooking) {
        console.log(`Found duplicate booking: ${existingBooking._id}`);
        return true;
      }

      console.log('No duplicate booking found');
      return false;
    } catch (error) {
      console.error('Error checking for duplicate booking:', error);
      // In case of error, assume there's a duplicate to prevent creating potentially duplicate bookings
      return true;
    }
  }

  /**
   * Check if a booking slot is already booked
   * @param bookingPageId The ID of the booking page
   * @param field The field ID
   * @param date The date of the booking
   * @param time The time of the booking
   * @returns True if the slot is already booked, false otherwise
   */
  async isSlotBooked(
    bookingPageId: string,
    field: string,
    date: string,
    time: string,
  ): Promise<boolean> {
    console.log(
      `Checking if slot is booked: bookingPageId=${bookingPageId}, field=${field}, date=${date}, time=${time}`,
    );

    try {
      // Convert date string to Date object for comparison
      const bookingDate = new Date(date);

      // Get start and end of the day for date range query
      const startOfDay = new Date(bookingDate);
      startOfDay.setHours(0, 0, 0, 0);

      const endOfDay = new Date(bookingDate);
      endOfDay.setHours(23, 59, 59, 999);

      console.log(
        `Date range: ${startOfDay.toISOString()} to ${endOfDay.toISOString()}`,
      );

      // First, find all bookings for this booking page with the specified field and time
      const bookings = await this.bookingModel
        .find({
          bookingPageId,
          status: { $in: ['pending', 'confirmed'] },
          'bookingSlots.field': field,
          'bookingSlots.time': time,
        })
        .exec();

      console.log(
        `Found ${bookings.length} bookings with matching field and time`,
      );

      // Then check if any of these bookings have a slot on the same date
      for (const booking of bookings) {
        for (const slot of booking.bookingSlots) {
          if (slot.field === field && slot.time === time) {
            const slotDate = new Date(slot.date);

            // Compare year, month, and day
            if (
              slotDate.getFullYear() === bookingDate.getFullYear() &&
              slotDate.getMonth() === bookingDate.getMonth() &&
              slotDate.getDate() === bookingDate.getDate()
            ) {
              console.log(
                `Found conflicting booking: ${booking._id}, slot date: ${slotDate.toISOString()}`,
              );
              return true;
            }
          }
        }
      }

      console.log('No conflicting bookings found');
      return false;
    } catch (error) {
      console.error('Error checking if slot is booked:', error);
      // In case of error, assume the slot is booked to prevent double booking
      return true;
    }
  }

  /**
   * Get all booked slots for a specific booking page and date
   * @param bookingPageId The ID of the booking page
   * @param date The date to check (YYYY-MM-DD)
   * @returns Array of booked slots with field, time, booking ID, and status
   */
  async getBookedSlots(
    bookingPageId: string,
    date: string,
  ): Promise<
    {
      field: string;
      time: string;
      date: string;
      bookingId: string;
      status: string;
    }[]
  > {
    console.log(
      `Getting booked slots for bookingPageId=${bookingPageId}, date=${date}`,
    );

    try {
      // Parse the date string to a Date object
      const bookingDate = new Date(date);

      // Get start and end of the day for date range query
      const startOfDay = new Date(bookingDate);
      startOfDay.setHours(0, 0, 0, 0);

      const endOfDay = new Date(bookingDate);
      endOfDay.setHours(23, 59, 59, 999);

      console.log(
        `Date range: ${startOfDay.toISOString()} to ${endOfDay.toISOString()}`,
      );

      // Find all bookings for this booking page (including all statuses)
      const bookings = await this.bookingModel
        .find({
          bookingPageId,
          // Include all statuses to show complete booking history
          // status: { $in: ['pending', 'confirmed', 'cancelled', 'completed'] },
        })
        .exec();

      console.log(`Found ${bookings.length} bookings for this booking page`);

      // Extract all booked slots for the specified date
      const bookedSlots: {
        field: string;
        time: string;
        date: string;
        bookingId: string;
        status: string;
      }[] = [];

      for (const booking of bookings) {
        for (const slot of booking.bookingSlots) {
          const slotDate = new Date(slot.date);

          // Compare year, month, and day to check if the slot is on the specified date
          if (
            slotDate.getFullYear() === bookingDate.getFullYear() &&
            slotDate.getMonth() === bookingDate.getMonth() &&
            slotDate.getDate() === bookingDate.getDate()
          ) {
            bookedSlots.push({
              field: slot.field,
              time: slot.time,
              date: slotDate.toISOString(),
              bookingId: booking._id.toString(),
              status: booking.status, // Add the booking status
            });
          }
        }
      }

      console.log(
        `Found ${bookedSlots.length} booked slots for the specified date`,
      );

      // Log the statuses for debugging
      const statusCounts = bookedSlots.reduce(
        (acc, slot) => {
          acc[slot.status] = (acc[slot.status] || 0) + 1;
          return acc;
        },
        {} as Record<string, number>,
      );

      console.log('Status counts:', statusCounts);

      return bookedSlots;
    } catch (error) {
      console.error(`Error getting booked slots: ${error.message}`, error);
      throw error;
    }
  }
}
