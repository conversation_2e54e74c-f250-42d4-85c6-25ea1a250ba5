import { Injectable, HttpException } from '@nestjs/common';
import * as bcrypt from 'bcrypt';
import * as crypto from 'crypto';

import { SignUpDto } from '../user/dto/signup.dto';
import { LoginDto } from './dto/login.dto';
import { UserService } from 'src/modules/user/user.service';
import { ErrorMessages } from './constants/error.constants';
import { RedisService } from 'src/common/services/redis.service';
import { getAccessToken, getRefreshToken } from 'src/constants/auth';
import { ErrorHelper } from 'src/common/services/error.helper';
import { ConfigService } from '@nestjs/config';
import { EnvConfigType } from 'src/config/env.config';
import { GoogleService } from '../../third-party/google/google.service';
import { GoogleLoginDto } from './dto/google-login.dto';
import { AllowedEmailService } from './services/allowed-email.service';

@Injectable()
export class AuthService {
  constructor(
    private readonly userService: UserService,
    private readonly redisService: RedisService,
    private readonly configService: ConfigService<EnvConfigType>,
    private readonly googleService: GoogleService,
    private readonly allowedEmailService: AllowedEmailService,
  ) {}

  // Đăng ký
  async signUp(signUpDto: SignUpDto) {
    const user = await this.userService.findByEmail(signUpDto?.email);

    if (user) {
      ErrorHelper.throwError({
        moduleErrors: ErrorMessages,
        key: 'account/exist-email',
      });
    }

    const result = await this.userService.createUser({
      ...signUpDto,
      emailVerified: false,
    });

    if (!result) {
      ErrorHelper.throwError({
        moduleErrors: ErrorMessages,
        key: 'account/create-user-failed',
      });
    }

    // Gửi email xác thực tài khoản
    this.userService.handleSendVerificationEmail(result);

    // send email
    return {
      email: result.email,
    };
  }

  // xử lý đăng ký, tạo ra link xác thực tài khoản gửi qua email

  // Đăng nhập
  async login(loginDto: LoginDto) {
    const { email, password } = loginDto;

    // Check if email is allowed to login
    const isAllowed = await this.allowedEmailService.isEmailAllowed(email);
    if (!isAllowed) {
      ErrorHelper.throwError({
        moduleErrors: ErrorMessages,
        key: 'account/email-not-allowed',
      });
    }

    const user = await this.userService.findFullUserByEmail(email);

    if (!user || !(await bcrypt.compare(password, user.password))) {
      ErrorHelper.throwError({
        moduleErrors: ErrorMessages,
        key: 'account/invalid-credentials',
      });
    }

    if (!user.emailVerified) {
      ErrorHelper.throwError({
        moduleErrors: ErrorMessages,
        key: 'account/unverified-email',
      });
    }

    return this.generateToken(user);
  }

  async generateToken(user: any) {
    const accessTokenInfo = await this.generateAccessToken(user);
    const refreshToken = await this.generateRefreshToken(user);

    return {
      accessToken: accessTokenInfo.accessToken,
      refreshToken,
      expiresIn: accessTokenInfo.expiresIn,
    };
  }

  // Tạo Access Token
  async generateAccessToken(user: any) {
    try {
      const accessToken = crypto.randomBytes(32).toString('hex');
      const ttl = this.configService.get('jwt.expirationTime');

      await this.redisService.setEx(getAccessToken(accessToken), user, ttl);

      return {
        accessToken,
        expiresIn: ttl,
      };
    } catch (error) {
      ErrorHelper.throwError({
        moduleErrors: ErrorMessages,
        key: 'account/internal-server-error',
        customMessage: error?.message,
      });
    }
  }

  // Tạo Refresh Token
  async generateRefreshToken(user: any) {
    try {
      const refreshToken = crypto.randomBytes(64).toString('hex');
      const ttl = this.configService.get('jwt.refreshTokenExpirationTime');

      await this.redisService.setEx(getRefreshToken(refreshToken), user, ttl);

      return refreshToken;
    } catch (error) {
      ErrorHelper.throwError({
        moduleErrors: ErrorMessages,
        key: 'account/internal-server-error',
        customMessage: error?.message,
      });
    }
  }

  // Xác thực Access Token
  async validateAccessToken(accessToken: string) {
    const user = await this.redisService.get(getAccessToken(accessToken));

    if (!user) {
      ErrorHelper.throwError({
        moduleErrors: ErrorMessages,
        key: 'account/access-token-expired',
      });
    }

    return JSON.parse(user);
  }

  // Làm mới Access Token bằng Refresh Token
  async refreshAccessToken(refreshToken: string) {
    const user = await this.redisService.get(getRefreshToken(refreshToken));

    if (!user) {
      ErrorHelper.throwError({
        moduleErrors: ErrorMessages,
        key: 'account/refresh-token-expired',
      });
    }

    const newAccessToken = await this.generateAccessToken(JSON.parse(user));
    return { accessToken: newAccessToken };
  }

  async refreshAccessTokenWithNewRefreshToken(refreshToken: string) {
    const user = await this.redisService.get(getRefreshToken(refreshToken));

    if (!user) {
      ErrorHelper.throwError({
        moduleErrors: ErrorMessages,
        key: 'account/refresh-token-expired',
      });
    }

    // Xóa Refresh Token cũ
    await this.redisService.del(getRefreshToken(refreshToken));

    // Tạo mới Access Token và Refresh Token
    const newAccessToken = await this.generateAccessToken(user);
    const newRefreshToken = await this.generateRefreshToken(user);

    return { accessToken: newAccessToken, refreshToken: newRefreshToken };
  }

  async loginWithGoogle(googleLoginDto: GoogleLoginDto) {
    try {
      // Xác thực token từ Google
      const googleUser = await this.googleService.verifyIdToken(
        googleLoginDto.idToken,
      );

      // Check if email is allowed to login
      const isAllowed = await this.allowedEmailService.isEmailAllowed(
        googleUser.email,
      );
      if (!isAllowed) {
        ErrorHelper.throwError({
          moduleErrors: ErrorMessages,
          key: 'account/email-not-allowed',
        });
      }

      // Kiểm tra xem user đã tồn tại chưa
      let user = await this.userService.findByEmail(googleUser.email);

      // Nếu chưa tồn tại, tạo user mới
      if (!user) {
        const randomPassword = crypto.randomBytes(32).toString('hex');
        user = await this.userService.createUser({
          email: googleUser.email,
          name: googleUser.name,
          googleId: googleUser.googleId,
          emailVerified: true,
          password: randomPassword, // Không cần password khi login bằng Google
        });
      } else if (!user?.googleId) {
        // Kiểm tra xem email đã được xác minh chưa
        if (!user.emailVerified) {
          // Nếu email chưa được xác minh, không cho phép liên kết với Google
          // ErrorHelper.throwError({
          //   moduleErrors: ErrorMessages,
          //   key: 'account/unverified-email',
          // });
        } else {
          // Chỉ liên kết Google ID nếu email đã được xác minh
          user = await this.userService.updateUser(user._id, {
            googleId: googleUser.googleId,
          });
        }
      }

      // Tạo mới Access Token và Refresh Token
      return this.generateToken(user);
    } catch (error) {
      // Nếu đã là lỗi từ ErrorHelper, truyền lại lỗi đó
      if (error instanceof HttpException) {
        throw error;
      }

      ErrorHelper.throwError({
        moduleErrors: ErrorMessages,
        key: 'account/internal-server-error',
        customMessage: error?.message,
      });
    }
  }

  /**
   * Resend verification email for non-authenticated users
   * @param email User email
   * @returns Success message
   */
  async resendVerificationEmail(email: string) {
    try {
      const user = await this.userService.findByEmail(email);

      if (!user) {
        ErrorHelper.throwError({
          moduleErrors: ErrorMessages,
          key: 'account/email-not-found',
        });
      }

      if (user.emailVerified) {
        ErrorHelper.throwError({
          moduleErrors: ErrorMessages,
          key: 'account/already-verified',
        });
      }

      return this.userService.resendVerificationEmailByEmail(email);
    } catch (error) {
      // Nếu đã là lỗi từ ErrorHelper, truyền lại lỗi đó
      if (error instanceof HttpException) {
        throw error;
      }

      ErrorHelper.throwError({
        moduleErrors: ErrorMessages,
        key: 'account/internal-server-error',
        customMessage: error?.message,
      });
    }
  }

  /**
   * Verify email with token
   * @param token Verification token
   * @returns Success message
   */
  async verifyEmail(token: string) {
    try {
      return this.userService.verifyEmail(token);
    } catch (error) {
      // Nếu đã là lỗi từ ErrorHelper, truyền lại lỗi đó
      if (error instanceof HttpException) {
        throw error;
      }

      ErrorHelper.throwError({
        moduleErrors: ErrorMessages,
        key: 'account/internal-server-error',
        customMessage: error?.message,
      });
    }
  }
}
