import { HttpException, HttpStatus } from '@nestjs/common';

export interface ErrorConfig<T> {
  key?: keyof T; // Key lỗi (có thể gợi ý từ moduleErrors)
  moduleErrors?: T; // Danh sách lỗi của module
  status?: HttpStatus; // Mã trạng thái HTTP (tùy chọn)
  customMessage?: string; // Thông báo lỗi tùy chỉnh (tùy chọn)
}

export class ErrorHelper {
  static throwError<T extends Record<string, string>>(config: ErrorConfig<T>) {
    const {
      key,
      moduleErrors,
      status = HttpStatus.BAD_REQUEST,
      customMessage,
    } = config;
    console.log('config', config);
    const message = customMessage || moduleErrors[key] || 'BAD REQUEST';
    throw new HttpException({ code: key, message }, status);
  }
}
