import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { User } from 'src/modules/user/schemas/user.schema';

// Type helper để lấy type của property
type PropertyType<T, K extends keyof T> = T[K];

export const GetUser = createParamDecorator(
  <K extends keyof User | undefined>(
    data: K,
    ctx: ExecutionContext,
  ): K extends keyof User ? PropertyType<User, K> : User => {
    const request = ctx.switchToHttp().getRequest();
    const user = request.user;

    return data ? user?.[data] : user;
  },
);
