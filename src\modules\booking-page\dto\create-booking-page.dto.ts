import { ApiProperty } from '@nestjs/swagger';
import {
  IsArray,
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsObject,
  IsOptional,
  IsString,
} from 'class-validator';
import {
  BannerBlock,
  DescriptionBlock,
  AvailabilityCalendarBlock,
  BookingFormBlock,
} from '../interfaces/blocks.interface';
import { PageTheme } from '../interfaces/theme.interface';

export class CreateBookingPageDto {
  @ApiProperty({
    description: 'Unique ID for the page',
    example: 'page-1746630824463',
  })
  @IsString()
  @IsOptional()
  id?: string;

  @ApiProperty({
    description: 'Name of the booking page',
    example: 'Trang đặt chỗ sự kiện',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Description of the booking page',
    example: 'Trang đặt chỗ cho sự kiện',
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'URL slug for the booking page',
    example: 'trang-dat-cho-su-kien',
  })
  @IsString()
  @IsNotEmpty()
  slug: string;

  @ApiProperty({ description: 'Template code', example: 'SPORT_FIELD_SLOTS' })
  @IsString()
  @IsNotEmpty()
  templateCode: string;

  @ApiProperty({ description: 'Content blocks for the page', type: 'array' })
  @IsArray()
  @IsNotEmpty()
  blocks: (
    | BannerBlock
    | DescriptionBlock
    | AvailabilityCalendarBlock
    | BookingFormBlock
  )[];

  @ApiProperty({
    description: 'Theme settings for the page',
    type: 'object',
    additionalProperties: true,
  })
  @IsObject()
  @IsNotEmpty()
  theme: PageTheme;

  @ApiProperty({
    description: 'Creation date',
    example: '2025-05-07T15:13:44.463Z',
  })
  @IsString()
  @IsOptional()
  createdAt?: string;

  @ApiProperty({
    description: 'Page status',
    example: 'active',
    enum: ['active', 'inactive', 'draft'],
  })
  @IsEnum(['active', 'inactive', 'draft'])
  @IsOptional()
  status?: string;

  ownerId: string;

  @ApiProperty({
    description: 'Whether the page is publicly accessible',
    example: true,
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  isPublic?: boolean;
}
