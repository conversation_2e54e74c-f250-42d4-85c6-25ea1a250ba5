import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EnvConfigType } from 'src/config/env.config';
import { IEmailProvider } from './email-provider.interface';
import { MailerSendService } from './mailersend.service';
import { ResendService } from './resend.service';

@Injectable()
export class EmailProviderFactory {
  constructor(
    private readonly config: ConfigService<EnvConfigType>,
    private readonly mailerSendService: MailerSendService,
    private readonly resendService: ResendService,
  ) {}

  getProvider(): IEmailProvider {
    const emailProvider = this.config.get('email.provider');

    switch (emailProvider) {
      case 'mailersend':
        return this.mailerSendService;
      case 'resend':
        return this.resendService;
      default:
        return this.mailerSendService;
    }
  }
}
