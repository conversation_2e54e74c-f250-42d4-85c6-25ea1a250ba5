import { forwardRef, Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { AuthService } from './auth.service';
import { AuthController } from './auth.controller';
import { UserModule } from '../user/user.module';
import { ThirdPartyModule } from '../../third-party/third-party.module';
import { CommonModule } from 'src/common/common.module';
import { AllowedEmailService } from './services/allowed-email.service';
import { AllowedEmailRepository } from './repositories/allowed-email.repository';
import { allowedEmailModelDefinition } from './schemas/allowed-email.schema';

@Module({
  imports: [
    forwardRef(() => CommonModule),
    UserModule,
    ThirdPartyModule,
    MongooseModule.forFeature([allowedEmailModelDefinition]),
  ],
  controllers: [AuthController],
  providers: [AuthService, AllowedEmailService, AllowedEmailRepository],
  exports: [AuthService, AllowedEmailService],
})
export class AuthModule {}
