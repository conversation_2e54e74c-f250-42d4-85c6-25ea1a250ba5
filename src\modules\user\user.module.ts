import { Module } from '@nestjs/common';
import { UserService } from './user.service';
import { UserController } from './user.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { userMoelDefinition } from './schemas/user.schema';
import { UserRepository } from './repositories/user.repository';
import { CommonModule } from 'src/common/common.module';
import { EmailModule } from '../email/email.module';
import { NotificationModule } from '../notification/notification.module';

@Module({
  imports: [
    MongooseModule.forFeature([userMoelDefinition]),
    CommonModule,
    EmailModule,
    NotificationModule,
  ],
  controllers: [UserController],
  providers: [UserService, UserRepository],
  exports: [UserService, UserRepository],
})
export class UserModule {}
