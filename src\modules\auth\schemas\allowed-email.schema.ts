import { ModelDefinition, Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { BaseSchema } from 'src/common/schemas/base.schema';
import * as validator from 'validator';

@Schema({
  collection: 'allowed_emails',
  versionKey: false,
})
export class AllowedEmail extends BaseSchema {
  @Prop({
    required: true,
    index: true,
    unique: true,
    validate: {
      validator: (value: string) => validator.isEmail(value),
      message: 'Invalid email address',
    },
  })
  email: string;

  @Prop({ required: false })
  description: string;

  @Prop({ required: true, default: Date.now })
  createdAt: Date;
}

export type AllowedEmailType = Omit<AllowedEmail, keyof BaseSchema | ''>;

export const AllowedEmailSchema = SchemaFactory.createForClass(AllowedEmail);

export const allowedEmailModelDefinition: ModelDefinition = {
  name: AllowedEmail.name,
  schema: AllowedEmailSchema,
};
