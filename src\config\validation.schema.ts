import * as Jo<PERSON> from 'joi';

export const validationSchema = Joi.object({
  MONGO_URI: Joi.string().required().messages({
    'string.uri': 'MONGO_URI must be a valid URI',
    'any.required': 'MONGO_URI is required',
  }),

  // Redis
  REDIS_URI: Joi.string().uri().required().messages({
    'string.uri': 'REDIS_URI must be a valid URI',
    'any.required': 'REDIS_URI is required',
  }),

  // JWT
  JWT_SECRET: Joi.string().default('secret').optional().messages({
    'string.base': 'JWT_SECRET must be a string',
  }),
  JWT_EXPIRATION_TIME: Joi.number().default(604800).optional().messages({
    'number.base': 'JWT_EXPIRATION_TIME must be a number',
  }),
  JWT_REFRESH_TOKEN_EXPIRATION_TIME: Joi.number()
    .default(2592000)
    .optional()
    .messages({
      'number.base': 'JWT_REFRESH_TOKEN_EXPIRATION_TIME must be a number',
    }),

  //  mailgun
  EMAIL_MODE: Joi.string().default('mailersend').messages({
    'any.required': 'EMAIL_MODE is required',
  }),
});
