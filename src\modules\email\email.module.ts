import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { MailerSendService } from './service/mailersend.service';
import { ResendService } from './service/resend.service';
import { EmailProviderFactory } from './service/email-provider.factory';
import { EmailTemplateService } from './service/template.service';

@Module({
  imports: [ConfigModule],
  providers: [
    MailerSendService,
    ResendService,
    EmailProviderFactory,
    EmailTemplateService,
  ],
  exports: [EmailProviderFactory],
})
export class EmailModule {}
