// Base interface for all block types
export interface BaseBlock {
  type: string;
  data: any;
}

// Banner block
export interface BannerBlock extends BaseBlock {
  type: 'banner';
  data: {
    image: string;
    title: string;
    subtitle: string;
  };
}

// Description block
export interface DescriptionBlock extends BaseBlock {
  type: 'description';
  data: {
    content: string;
  };
}

// Field interface for availability calendar
export interface Field {
  id: string;
  name: string;
  type: string;
  capacity: number;
  pricePerHour: number;
  dynamicPricing: {
    enabled: boolean;
    timeBasedPrices: any[];
    dayBasedPrices: any[];
    combinedRules: any[];
  };
}

// Business hours interface
export interface BusinessHours {
  start: string;
  end: string;
  daysOfWeek: number[];
}

// Availability calendar block
export interface AvailabilityCalendarBlock extends BaseBlock {
  type: 'availability_calendar';
  data: {
    title: string;
    subtitle: string;
    view: string;
    defaultView: string;
    businessHours: BusinessHours;
    showWeekends: boolean;
    firstDayOfWeek: number;
    timeSlotInterval: number;
    showAvailabilityLegend: boolean;
    fields: Field[];
  };
}

// Booking form block
export interface BookingFormBlock extends BaseBlock {
  type: 'booking_form';
  data: {
    fields: string[];
    payment_methods: string[];
  };
}
