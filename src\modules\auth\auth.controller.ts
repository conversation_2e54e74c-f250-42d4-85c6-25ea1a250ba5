import {
  Body,
  Controller,
  Post,
  Get,
  Delete,
  UseGuards,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { AuthService } from './auth.service';
import { SignUpDto } from '../user/dto/signup.dto';
import { LoginDto } from './dto/login.dto';
import {
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { RefreshTokenDto } from './dto/refresh-token.dto';
import { GoogleLoginDto } from './dto/google-login.dto';
import { ResendVerificationDto } from './dto/resend-verification.dto';
import { VerifyEmailDto } from './dto/verify-email.dto';
import { AllowedEmailService } from './services/allowed-email.service';
import {
  AddAllowedEmailDto,
  RemoveAllowedEmailDto,
} from './dto/allowed-email.dto';
import { AuthGuard } from 'src/common/guards/auth.guard';

@ApiTags('auth')
@Controller('auth')
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly allowedEmailService: AllowedEmailService,
  ) {}

  @ApiOperation({ summary: 'Create a user' })
  @ApiResponse({ status: 200, description: 'User created successfully' })
  @Post('signup')
  async signUp(@Body() signUpDto: SignUpDto) {
    const data = await this.authService.signUp(signUpDto);
    return { data };
  }

  @ApiOperation({ summary: 'Signin' })
  @ApiResponse({ status: 200, description: 'Signin successfully' })
  @Post('signin')
  async signIn(@Body() payload: LoginDto) {
    const data = await this.authService.login(payload);
    return { data };
  }

  @ApiOperation({ summary: 'Signin with Google' })
  @ApiResponse({ status: 200, description: 'Signin with Google successfully' })
  @Post('google-signin')
  async googleSignIn(@Body() payload: GoogleLoginDto) {
    const data = await this.authService.loginWithGoogle(payload);
    return { data };
  }

  @ApiOperation({ summary: 'Refresh token' })
  @ApiResponse({ status: 200, description: 'Refresh token successfully' })
  @Post('refresh-token')
  async refreshToken(@Body() payload: RefreshTokenDto) {
    const data = await this.authService.refreshAccessTokenWithNewRefreshToken(
      payload?.refreshToken,
    );
    return { data };
  }

  @ApiOperation({ summary: 'Resend verification email' })
  @ApiResponse({
    status: 200,
    description: 'Verification email sent successfully',
  })
  @ApiResponse({ status: 404, description: 'User not found' })
  @ApiResponse({ status: 429, description: 'Rate limit exceeded' })
  @Post('resend-verification')
  @UsePipes(new ValidationPipe())
  async resendVerification(@Body() payload: ResendVerificationDto) {
    const data = await this.authService.resendVerificationEmail(payload.email);
    return { data };
  }

  @ApiOperation({ summary: 'Verify email with token (POST method)' })
  @ApiResponse({ status: 200, description: 'Email verified successfully' })
  @ApiResponse({ status: 400, description: 'Invalid or expired token' })
  @ApiResponse({ status: 404, description: 'User not found' })
  @Post('verify-email')
  @UsePipes(new ValidationPipe())
  async verifyEmailPost(@Body() payload: VerifyEmailDto) {
    const data = await this.authService.verifyEmail(payload.token);
    return { data };
  }

  // Allowed Emails Management Endpoints (Admin only)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get all allowed emails' })
  @ApiResponse({ status: 200, description: 'List of allowed emails' })
  @UseGuards(AuthGuard)
  @Get('allowed-emails')
  async getAllowedEmails() {
    const data = await this.allowedEmailService.getAllowedEmails();
    return { data };
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Add an email to allowed list' })
  @ApiResponse({ status: 200, description: 'Email added to allowed list' })
  @UseGuards(AuthGuard)
  @Post('allowed-emails')
  async addAllowedEmail(@Body() payload: AddAllowedEmailDto) {
    const data = await this.allowedEmailService.addAllowedEmail(
      payload.email,
      payload.description,
    );
    return { data };
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Remove an email from allowed list' })
  @ApiResponse({ status: 200, description: 'Email removed from allowed list' })
  @UseGuards(AuthGuard)
  @Delete('allowed-emails')
  async removeAllowedEmail(@Body() payload: RemoveAllowedEmailDto) {
    const data = await this.allowedEmailService.removeAllowedEmail(
      payload.email,
    );
    return { data };
  }
}
