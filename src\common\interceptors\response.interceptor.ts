import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

export interface Response<T> {
  data: T;
}

@Injectable()
export class ResponseInterceptor<T> implements NestInterceptor<T, Response<T>> {
  intercept(
    context: ExecutionContext,
    next: CallHandler,
  ): Observable<Response<T>> {
    return next.handle().pipe(
      map((result) => {
        if (Array.isArray(result) || typeof result !== 'object') {
          return result;
        }

        if (result.isStatic) {
          return result;
        }

        const { data, status, meta, code, message, type } = result;
        const response: any = {};

        if (status) {
          response.status = status;
        } else {
          response.status = {
            success: true,
            code: code || 200,
            message: message || 'Success',
          };
        }

        response.data = data;

        if (meta) {
          response.meta = meta || null;
        }

        if (type) {
          response.set({
            'Content-Type': type,
          });
        }

        return response;
      }),
    );
  }
}
