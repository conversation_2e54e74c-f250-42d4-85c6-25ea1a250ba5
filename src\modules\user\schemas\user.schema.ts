import { ModelDefinition, Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { BaseSchema } from 'src/common/schemas/base.schema';

// Importing validator for email validation
import * as validator from 'validator';

@Schema({
  collection: 'users',
  versionKey: false, // Bỏ trường __v (version key)
}) // Tên collection là 'users'
export class User extends BaseSchema {
  @Prop({
    required: true,
    index: true, // Tạo index cho trường email
    unique: true,
    validate: {
      validator: (value: string) => validator.isEmail(value), // Validate email
      message: 'Invalid email address', // Thông báo lỗi khi không phải email hợp lệ
    },
  })
  email: string;

  @Prop({ required: true, select: false })
  password: string;

  @Prop({ required: true })
  name: string;

  @Prop({ default: false })
  emailVerified: boolean;

  @Prop({ required: false })
  googleId?: string;
}

// Tạo Type tự động từ Schema
export type UserType = Omit<User, Exclude<keyof BaseSchema, '_id'>>;

export const UserSchema = SchemaFactory.createForClass(User);

export const userMoelDefinition: ModelDefinition = {
  name: User.name,
  schema: UserSchema,
};
