import { ModelDefinition, Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { BaseSchema } from 'src/common/schemas/base.schema';

@Schema({
  collection: 'notifications',
})
export class Notifications extends BaseSchema {
  @Prop({ required: true })
  type: string;

  @Prop({ required: true })
  to: string;

  @Prop({ required: true })
  content: string;

  @Prop({ required: false })
  subject?: string;

  @Prop({ required: true, default: 'pending' })
  status: string;

  createdAt: Date;
}

// Tạo Type tự động từ Schema
export type NotificationType = Omit<Notifications, keyof BaseSchema | ''>;

export const NotificationSchema = SchemaFactory.createForClass(Notifications);

export const notificationModelDefinition: ModelDefinition = {
  name: Notifications.name,
  schema: NotificationSchema,
};
