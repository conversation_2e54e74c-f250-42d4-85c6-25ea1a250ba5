import {
  Controller,
  Get,
  Param,
  Query,
  NotFoundException,
} from '@nestjs/common';
import { BookingPageService } from '../booking-page.service';
import { ApiOperation, ApiResponse, ApiTags, ApiQuery } from '@nestjs/swagger';

@ApiTags('public/booking-page')
@Controller('public/booking-page')
export class BookingPagePublicController {
  constructor(private readonly bookingPageService: BookingPageService) {}

  @Get()
  @ApiOperation({ summary: 'Get all public booking pages' })
  @ApiResponse({ status: 200, description: 'Return all public booking pages' })
  @ApiQuery({
    name: 'ownerId',
    required: false,
    description: 'Filter by owner ID',
  })
  async findAll(@Query('ownerId') ownerId?: string) {
    // End users can only see public pages
    const data = await this.bookingPageService.findAll({
      ownerId,
      includePrivate: false, // End users can only see public pages
    });
    return { data };
  }

  @Get('slug/:slug')
  @ApiOperation({ summary: 'Get a public booking page by slug' })
  @ApiResponse({ status: 200, description: 'Return the booking page' })
  @ApiResponse({
    status: 404,
    description: 'Booking page not found or not public',
  })
  async findBySlug(@Param('slug') slug: string) {
    try {
      // End users can only see public pages
      const data = await this.bookingPageService.findBySlug(slug);

      // Check if the page is public
      if (!data.isPublic) {
        throw new NotFoundException('Booking page not found or not public');
      }

      return { data };
    } catch (error: any) {
      throw new NotFoundException(
        error?.message || 'Booking page not found or not public',
      );
    }
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a public booking page by ID' })
  @ApiResponse({ status: 200, description: 'Return the booking page' })
  @ApiResponse({
    status: 404,
    description: 'Booking page not found or not public',
  })
  async findOne(@Param('id') id: string) {
    try {
      // End users can only see public pages
      const data = await this.bookingPageService.findOne(id);

      // Check if the page is public
      if (!data.isPublic) {
        throw new NotFoundException('Booking page not found or not public');
      }

      return { data };
    } catch (error: any) {
      throw new NotFoundException(
        error?.message || 'Booking page not found or not public',
      );
    }
  }
}
