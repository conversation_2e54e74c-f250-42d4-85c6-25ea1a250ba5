import { Injectable, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { BaseRepository } from 'src/common/repositories/base.repository';
import { AllowedEmail } from '../schemas/allowed-email.schema';

@Injectable()
export class AllowedEmailRepository extends BaseRepository<AllowedEmail> {
  constructor(
    @InjectModel(AllowedEmail.name)
    private readonly allowedEmailModel: Model<AllowedEmail>,
  ) {
    super(allowedEmailModel);
  }

  async createAllowedEmail(data: {
    email: string;
    description?: string;
  }): Promise<AllowedEmail> {
    try {
      return this.create(data);
    } catch (error) {
      throw new BadRequestException({ message: error?.message });
    }
  }

  async findByEmail(email: string): Promise<AllowedEmail | null> {
    return this.allowedEmailModel.findOne({ email }).exec();
  }

  async deleteByEmail(email: string): Promise<boolean> {
    const result = await this.allowedEmailModel.deleteOne({ email }).exec();
    return result.deletedCount > 0;
  }
}
