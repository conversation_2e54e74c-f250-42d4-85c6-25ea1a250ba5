import { Injectable, HttpException, Logger } from '@nestjs/common';
import { AllowedEmailRepository } from '../repositories/allowed-email.repository';
import { ErrorHelper } from 'src/common/services/error.helper';
import { ErrorMessages } from '../constants/error.constants';
import { AllowedEmail } from '../schemas/allowed-email.schema';

@Injectable()
export class AllowedEmailService {
  private readonly logger = new Logger(AllowedEmailService.name);

  constructor(
    private readonly allowedEmailRepository: AllowedEmailRepository,
  ) {}

  /**
   * Check if an email is allowed to login
   * @param email Email to check
   * @returns true if email is allowed, false otherwise
   */
  async isEmailAllowed(email: string): Promise<boolean> {
    try {
      // If there are no allowed emails in the database, all emails are allowed
      const count = await this.allowedEmailRepository.count();
      if (count === 0) {
        return true;
      }

      // Check if the email is in the allowed list
      const allowedEmail = await this.allowedEmailRepository.findByEmail(email);
      return !!allowedEmail;
    } catch (error) {
      this.logger.error(error);
      return false;
    }
  }

  /**
   * Add an email to the allowed list
   * @param email Email to add
   * @param description Optional description
   * @returns The created allowed email
   */
  async addAllowedEmail(
    email: string,
    description?: string,
  ): Promise<AllowedEmail> {
    try {
      // Check if email already exists in the allowed list
      const existingEmail =
        await this.allowedEmailRepository.findByEmail(email);
      if (existingEmail) {
        ErrorHelper.throwError({
          moduleErrors: ErrorMessages,
          key: 'account/allowed-email-exists',
        });
      }

      // Add email to the allowed list
      return this.allowedEmailRepository.createAllowedEmail({
        email,
        description,
      });
    } catch (error) {
      // If already an error from ErrorHelper, rethrow it
      if (error instanceof HttpException) {
        throw error;
      }

      ErrorHelper.throwError({
        moduleErrors: ErrorMessages,
        key: 'account/internal-server-error',
        customMessage: error?.message,
      });
    }
  }

  /**
   * Remove an email from the allowed list
   * @param email Email to remove
   * @returns true if email was removed, false otherwise
   */
  async removeAllowedEmail(email: string): Promise<boolean> {
    try {
      // Check if email exists in the allowed list
      const existingEmail =
        await this.allowedEmailRepository.findByEmail(email);
      if (!existingEmail) {
        ErrorHelper.throwError({
          moduleErrors: ErrorMessages,
          key: 'account/allowed-email-not-found',
        });
      }

      // Remove email from the allowed list
      return this.allowedEmailRepository.deleteByEmail(email);
    } catch (error) {
      // If already an error from ErrorHelper, rethrow it
      if (error instanceof HttpException) {
        throw error;
      }

      ErrorHelper.throwError({
        moduleErrors: ErrorMessages,
        key: 'account/internal-server-error',
        customMessage: error?.message,
      });
    }
  }

  /**
   * Get all allowed emails
   * @returns List of allowed emails
   */
  async getAllowedEmails(): Promise<AllowedEmail[]> {
    try {
      return this.allowedEmailRepository.find({});
    } catch (error) {
      ErrorHelper.throwError({
        moduleErrors: ErrorMessages,
        key: 'account/internal-server-error',
        customMessage: error?.message,
      });
    }
  }
}
