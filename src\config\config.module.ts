import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';

import envConfig from './env.config';
import { validationSchema } from './validation.schema';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [envConfig],
      validationSchema,
      validationOptions: {
        allowUnknown: true, // Cho phép các biến không nằm trong schema
        abortEarly: true,
      },
    }),
  ],
})
export class ConfigurationModule {}
