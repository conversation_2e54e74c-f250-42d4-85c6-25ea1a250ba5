import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { BaseRepository } from 'src/common/repositories/base.repository';
import { BookingPage } from '../schemas/booking-page.schema';
import { Model } from 'mongoose';

@Injectable()
export class BookingPageRepository extends BaseRepository<BookingPage> {
  constructor(
    @InjectModel(BookingPage.name) private bookingPageModel: Model<BookingPage>,
  ) {
    super(bookingPageModel);
  }

  async findBySlug(slug: string): Promise<BookingPage | null> {
    console.log(`Repository: Finding booking page by slug: ${slug}`);
    try {
      const page = await this.bookingPageModel.findOne({ slug }).exec();
      if (page) {
        console.log(`Repository: Found booking page with ID: ${page._id}`);
      } else {
        console.log(`Repository: No booking page found with slug: ${slug}`);
      }
      return page;
    } catch (error) {
      console.error(
        `Repository: Error finding booking page by slug ${slug}:`,
        error,
      );
      throw error;
    }
  }

  async findByTemplateCode(templateCode: string): Promise<BookingPage[]> {
    return this.bookingPageModel.find({ templateCode }).exec();
  }

  async deleteById(id: string): Promise<void> {
    await this.bookingPageModel.findByIdAndDelete(id).exec();
  }
}
