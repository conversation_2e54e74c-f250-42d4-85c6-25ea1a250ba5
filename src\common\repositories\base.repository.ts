import { Model, Document } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';
import { Injectable } from '@nestjs/common';

@Injectable()
export abstract class BaseRepository<T extends Document> {
  constructor(@InjectModel('T') protected readonly model: Model<T>) {}

  // Tìm tất cả các bản ghi
  async findAll(): Promise<T[]> {
    return this.model.find().exec();
  }

  // Tìm tất cả các bản ghi
  async find(query: object): Promise<T[]> {
    return this.model.find(query).exec();
  }

  // Tìm 1 bản ghi theo điều kiện
  async findOne(condition: object): Promise<T | null> {
    return this.model.findOne(condition).exec();
  }

  findById(id: string): Promise<T | null> {
    return this.model.findById(id).exec();
  }

  // Tạo mới bản ghi
  async create(createDto: any): Promise<T> {
    const createdDoc = new this.model(createDto);
    return createdDoc.save();
  }

  // Cập nhật bản ghi
  async update(id: string, updateDto: any): Promise<T | null> {
    return this.model.findByIdAndUpdate(id, updateDto, { new: true }).exec();
  }

  // Xóa bản ghi
  async delete(id: string): Promise<T | null> {
    return this.model.findByIdAndDelete(id).exec();
  }

  async findByIdAndUpdate(
    id: string,
    updateDto: any,
    options: { new: boolean } = { new: true },
  ): Promise<T | null> {
    return this.model.findByIdAndUpdate(id, updateDto, options).exec();
  }

  findByIdAndDelete(id: string): Promise<T | null> {
    return this.model.findByIdAndDelete(id).exec();
  }

  count(query?: object): Promise<number> {
    return this.model.countDocuments(query).exec();
  }
}
