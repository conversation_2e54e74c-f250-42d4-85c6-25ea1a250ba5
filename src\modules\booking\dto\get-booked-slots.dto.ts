import { ApiProperty } from '@nestjs/swagger';
import { IsDateString, IsMongoId, IsNotEmpty } from 'class-validator';

export class GetBookedSlotsDto {
  @ApiProperty({
    description: 'Booking page ID',
    example: '68261cde094b5f3e38d3ae25',
  })
  @IsMongoId()
  @IsNotEmpty()
  bookingPageId: string;

  @ApiProperty({
    description: 'Date to check for booked slots (YYYY-MM-DD)',
    example: '2025-05-22',
  })
  @IsDateString()
  @IsNotEmpty()
  date: string;
}
